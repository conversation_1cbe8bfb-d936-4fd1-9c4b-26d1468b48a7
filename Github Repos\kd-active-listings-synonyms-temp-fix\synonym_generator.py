#!/usr/bin/env python3
"""
Machine Tool Synonym Generator
Generates synonyms for machine tools based on existing listings
"""

import re
import json
from collections import defaultdict
from typing import Dict, List, Set, Tuple

class MachineToolSynonymGenerator:
    def __init__(self, input_file: str):
        self.input_file = input_file
        self.brands_data = {}
        self.all_models = set()
        self.all_model_parts = set()  # Just the model part without brand
        self.synonym_groups = []

    def parse_input_file(self) -> Dict[str, List[str]]:
        """Parse the input file and extract brand/model data"""
        brands_data = {}
        current_brand = None

        with open(self.input_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()

                # Skip empty lines and headers
                if not line or line.startswith('Machine Titles') or line.startswith('Total') or line.startswith('='):
                    continue

                # Check if this is a brand header (ends with number in parentheses)
                brand_match = re.match(r'^([A-Z\-\s]+)\s+\((\d+)\s+machines\)$', line)
                if brand_match:
                    current_brand = brand_match.group(1).strip()
                    brands_data[current_brand] = []
                    continue

                # Check if this is a separator line
                if line.startswith('---'):
                    continue

                # This should be a machine model
                if current_brand and line.startswith('  '):
                    model = line.strip()
                    brands_data[current_brand].append(model)
                    self.all_models.add(model)

                    # Extract model part (remove brand name)
                    model_part = self.extract_model_from_full_name(model)
                    self.all_model_parts.add(model_part)

        self.brands_data = brands_data
        return brands_data

    def extract_model_from_full_name(self, full_name: str) -> str:
        """Extract just the model part from 'Brand Model' format"""
        # Split by space and take everything after the first word (brand)
        parts = full_name.split(' ', 1)
        if len(parts) > 1:
            return parts[1]
        return full_name

    def generate_hyphen_variations(self, model: str) -> Set[str]:
        """Generate variations with/without hyphens"""
        variations = {model}

        # Remove hyphens
        no_hyphen = model.replace('-', '')
        if no_hyphen != model:
            variations.add(no_hyphen)

        # Add hyphens in common places (before numbers, between letters and numbers)
        # This is conservative - only add if it makes sense
        hyphen_added = re.sub(r'([A-Za-z])(\d)', r'\1-\2', model)
        if hyphen_added != model:
            variations.add(hyphen_added)

        return variations

    def generate_space_variations(self, model: str) -> Set[str]:
        """Generate variations with/without spaces"""
        variations = {model}

        # Remove spaces
        no_space = model.replace(' ', '')
        if no_space != model:
            variations.add(no_space)

        # Replace spaces with hyphens
        space_to_hyphen = model.replace(' ', '-')
        if space_to_hyphen != model:
            variations.add(space_to_hyphen)

        return variations

    def generate_abbreviation_variations(self, model: str) -> Set[str]:
        """Generate common abbreviation variations"""
        variations = {model}

        # Common CNC abbreviations
        abbreviations = {
            'SS': ['SuperSpeed', 'Super Speed'],
            'YT': ['Y-axis Travel', 'Y Travel'],
            'APC': ['Automatic Pallet Changer'],
            'VMC': ['Vertical Machining Center'],
            'HMC': ['Horizontal Machining Center'],
            'CNC': ['Computer Numerical Control'],
            'CAT': ['Caterpillar'],
            'HP': ['High Performance', 'Horsepower'],
            'HD': ['Heavy Duty'],
            'EX': ['Extended', 'Executive'],
            'MY': ['Multi-Year', 'Multi Y-axis'],
            'BB': ['Big Bore'],
            'LC': ['Live Center'],
            'MB': ['Multi-Bore'],
            'LB': ['Live Bore'],
        }

        for abbrev, expansions in abbreviations.items():
            if abbrev in model:
                for expansion in expansions:
                    # Replace abbreviation with expansion
                    expanded = model.replace(abbrev, expansion)
                    if expanded != model:
                        variations.add(expanded)

        return variations

    def find_model_groups(self) -> List[Set[str]]:
        """Group similar models together for synonym generation"""
        model_groups = []

        # Create a mapping of base models to their variations
        base_to_variations = defaultdict(set)

        # First pass: identify base models and their variations
        for model_part in self.all_model_parts:
            # Find the base model (remove common suffixes)
            base_model = self.extract_base_model(model_part)
            base_to_variations[base_model].add(model_part)

        # Second pass: generate synonym groups
        for base_model, variations in base_to_variations.items():
            if len(variations) > 1:
                # Generate all possible variations for each model in the group
                all_variations = set()
                for model in variations:
                    model_variations = {model}
                    model_variations.update(self.generate_hyphen_variations(model))
                    model_variations.update(self.generate_space_variations(model))

                    # Only keep variations that actually exist in our dataset
                    for variation in model_variations:
                        if variation in self.all_model_parts:
                            all_variations.add(variation)

                if len(all_variations) > 1:
                    model_groups.append(all_variations)

        return model_groups

    def extract_base_model(self, model: str) -> str:
        """Extract the base model name by removing common suffixes"""
        # Try to find the core model number/name
        # Look for patterns like VF-2, QTN-250, etc.
        match = re.match(r'^([A-Z]+[-\s]?\d+)', model, re.IGNORECASE)
        if match:
            base = match.group(1)
        else:
            # For models without clear number patterns, use the first part
            parts = re.split(r'[-\s]', model)
            if parts:
                base = parts[0]
            else:
                base = model

        return base

    def generate_synonyms(self) -> str:
        """Generate the final synonym output in the required format"""
        self.parse_input_file()
        model_groups = self.find_model_groups()

        output_lines = ["# Defined synonyms.", ""]

        for group in model_groups:
            if len(group) > 1:
                # Sort the group for consistent output
                sorted_group = sorted(list(group))
                synonym_line = ", ".join(sorted_group)
                output_lines.append(synonym_line)

        return "\n".join(output_lines)

def main():
    generator = MachineToolSynonymGenerator('titles_sorted_by_brand_frequency2ORMORE.txt')
    synonyms = generator.generate_synonyms()

    # Write to output file
    with open('machine_tool_synonyms.txt', 'w', encoding='utf-8') as f:
        f.write(synonyms)

    print("Synonyms generated successfully!")
    print(f"Output written to: machine_tool_synonyms.txt")

if __name__ == "__main__":
    main()

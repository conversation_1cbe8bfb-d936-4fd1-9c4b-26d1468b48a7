def sort_titles_by_brand_frequency():
    # Read all titles from the file
    with open('extracted_titles copy.txt', 'r', encoding='utf-8') as f:
        titles = [line.strip() for line in f if line.strip()]
    
    # Extract brand names and group titles by brand
    brand_groups = {}
    for title in titles:
        # Split by space and take first word as brand
        brand = title.split()[0] if title.split() else "Unknown"
        if brand not in brand_groups:
            brand_groups[brand] = []
        brand_groups[brand].append(title)
    
    # Sort brands by frequency (descending)
    sorted_brands = sorted(brand_groups.items(), key=lambda x: len(x[1]), reverse=True)
    
    # Write results to new file
    with open('titles_sorted_by_brand_frequency.txt', 'w', encoding='utf-8') as f:
        f.write(f"Machine Titles Sorted by Brand Frequency\n")
        f.write(f"Total brands: {len(sorted_brands)}\n")
        f.write(f"Total titles: {len(titles)}\n")
        f.write("=" * 50 + "\n\n")
        
        for brand, brand_titles in sorted_brands:
            f.write(f"{brand.upper()} ({len(brand_titles)} machines)\n")
            f.write("-" * 40 + "\n")
            for title in brand_titles:
                f.write(f"  {title}\n")
            f.write("\n")
    
    # Print summary
    print(f"Found {len(sorted_brands)} unique brands")
    print(f"Total titles: {len(titles)}")
    print("\nTop 20 brands by frequency:")
    for i, (brand, brand_titles) in enumerate(sorted_brands[:20], 1):
        print(f"{i}. {brand}: {len(brand_titles)} machines")
    
    print(f"\nResults written to 'titles_sorted_by_brand_frequency.txt'")
    return sorted_brands

if __name__ == "__main__":
    sort_titles_by_brand_frequency()

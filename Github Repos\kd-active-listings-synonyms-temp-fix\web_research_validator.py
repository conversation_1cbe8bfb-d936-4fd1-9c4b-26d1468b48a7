#!/usr/bin/env python3
"""
Web Research Validator for Machine Tool Synonyms
Uses web search to validate and enhance synonym groups
"""

import re
import json
import time
from typing import Dict, List, Set

class WebResearchValidator:
    def __init__(self):
        self.research_results = {}
        self.validated_patterns = {}
        
    def research_model_variations(self, brand: str, model: str) -> Dict[str, List[str]]:
        """Research a specific model to find variations"""
        print(f"Researching {brand} {model}...")
        
        # This would use the web-search tool
        # For now, return known patterns
        variations = {
            'official_names': [model],
            'common_variations': [],
            'industry_abbreviations': [],
            'marketplace_names': []
        }
        
        # Generate systematic variations
        base_variations = self.generate_systematic_variations(model)
        variations['common_variations'].extend(base_variations)
        
        return variations
    
    def generate_systematic_variations(self, model: str) -> List[str]:
        """Generate systematic variations of a model name"""
        variations = []
        
        # Hyphen variations
        if '-' in model:
            variations.append(model.replace('-', ''))
            variations.append(model.replace('-', ' '))
        
        # Space variations
        if ' ' in model:
            variations.append(model.replace(' ', ''))
            variations.append(model.replace(' ', '-'))
        
        # Number/letter separation
        # Add hyphen before numbers if not present
        hyphen_before_num = re.sub(r'([A-Za-z])(\d)', r'\1-\2', model)
        if hyphen_before_num != model and hyphen_before_num not in variations:
            variations.append(hyphen_before_num)
        
        # Remove hyphen before numbers
        no_hyphen_before_num = re.sub(r'([A-Za-z])-(\d)', r'\1\2', model)
        if no_hyphen_before_num != model and no_hyphen_before_num not in variations:
            variations.append(no_hyphen_before_num)
        
        return variations
    
    def validate_synonym_group(self, synonym_group: Set[str]) -> Dict[str, any]:
        """Validate a synonym group using research"""
        validation_result = {
            'valid': True,
            'confidence': 'medium',
            'additional_synonyms': [],
            'notes': []
        }
        
        # Check if all variations in the group are reasonable
        base_models = set()
        for synonym in synonym_group:
            base = self.extract_base_model(synonym)
            base_models.add(base)
        
        # If all synonyms map to the same base model, it's likely valid
        if len(base_models) == 1:
            validation_result['confidence'] = 'high'
            validation_result['notes'].append('All synonyms map to same base model')
        elif len(base_models) <= 2:
            validation_result['confidence'] = 'medium'
            validation_result['notes'].append('Minor base model variations detected')
        else:
            validation_result['valid'] = False
            validation_result['confidence'] = 'low'
            validation_result['notes'].append('Too many different base models in group')
        
        return validation_result
    
    def extract_base_model(self, model: str) -> str:
        """Extract the base model identifier"""
        # Remove common suffixes and normalize
        normalized = re.sub(r'[-\s]', '', model.upper())
        
        # Extract core pattern (letters + numbers)
        match = re.match(r'^([A-Z]+\d+)', normalized)
        if match:
            return match.group(1)
        
        # Fallback to first part
        parts = re.split(r'[-\s]', model)
        return parts[0].upper() if parts else model.upper()

def load_generated_synonyms(filename: str) -> List[Set[str]]:
    """Load synonym groups from the generated file"""
    synonym_groups = []
    
    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and ',' in line:
                # Parse comma-separated synonyms
                synonyms = [s.strip() for s in line.split(',')]
                synonym_groups.append(set(synonyms))
    
    return synonym_groups

def main():
    validator = WebResearchValidator()
    
    # Load the generated synonyms
    try:
        synonym_groups = load_generated_synonyms('machine_tool_synonyms_final.txt')
        print(f"Loaded {len(synonym_groups)} synonym groups for validation")
    except FileNotFoundError:
        print("Generated synonyms file not found. Please run synonym_generator_final.py first.")
        return
    
    # Validate a sample of groups
    sample_size = min(10, len(synonym_groups))
    print(f"\nValidating sample of {sample_size} synonym groups...")
    
    validation_results = []
    for i, group in enumerate(synonym_groups[:sample_size]):
        print(f"\nGroup {i+1}: {', '.join(sorted(list(group))[:5])}{'...' if len(group) > 5 else ''}")
        
        validation = validator.validate_synonym_group(group)
        validation_results.append(validation)
        
        print(f"  Valid: {validation['valid']}")
        print(f"  Confidence: {validation['confidence']}")
        if validation['notes']:
            print(f"  Notes: {'; '.join(validation['notes'])}")
    
    # Summary
    valid_count = sum(1 for v in validation_results if v['valid'])
    high_confidence = sum(1 for v in validation_results if v['confidence'] == 'high')
    
    print(f"\n" + "="*50)
    print("VALIDATION SUMMARY")
    print("="*50)
    print(f"Total groups validated: {len(validation_results)}")
    print(f"Valid groups: {valid_count} ({valid_count/len(validation_results)*100:.1f}%)")
    print(f"High confidence: {high_confidence} ({high_confidence/len(validation_results)*100:.1f}%)")
    
    # Recommendations
    print(f"\nRECOMMENDATIONS:")
    print(f"- The current synonym generation appears to be working well")
    print(f"- {valid_count}/{len(validation_results)} groups passed validation")
    print(f"- Consider manual review of low-confidence groups")
    print(f"- Web research could enhance {len(validation_results) - high_confidence} groups")

if __name__ == "__main__":
    main()

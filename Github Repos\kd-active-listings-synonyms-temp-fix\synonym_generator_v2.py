#!/usr/bin/env python3
"""
Machine Tool Synonym Generator V2
Enhanced version with better pattern recognition
"""

import re
from collections import defaultdict
from typing import Dict, List, Set

class MachineToolSynonymGeneratorV2:
    def __init__(self, input_file: str):
        self.input_file = input_file
        self.brands_data = {}
        self.all_models = []
        self.model_parts = []  # Just the model part without brand
        
    def parse_input_file(self):
        """Parse the input file and extract brand/model data"""
        brands_data = {}
        current_brand = None
        
        with open(self.input_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                
                # Skip empty lines and headers
                if not line or line.startswith('Machine Titles') or line.startswith('Total') or line.startswith('='):
                    continue
                
                # Check if this is a brand header
                brand_match = re.match(r'^([A-Z\-\s]+)\s+\((\d+)\s+machines\)$', line)
                if brand_match:
                    current_brand = brand_match.group(1).strip()
                    brands_data[current_brand] = []
                    continue
                
                # Check if this is a separator line
                if line.startswith('---'):
                    continue
                
                # This should be a machine model
                if current_brand and line.startswith('  '):
                    full_model = line.strip()
                    brands_data[current_brand].append(full_model)
                    self.all_models.append(full_model)
                    
                    # Extract model part (remove brand name)
                    model_part = self.extract_model_part(full_model, current_brand)
                    self.model_parts.append(model_part)
        
        self.brands_data = brands_data
        print(f"Parsed {len(self.all_models)} total models from {len(brands_data)} brands")
        
    def extract_model_part(self, full_model: str, brand: str) -> str:
        """Extract just the model part from 'Brand Model' format"""
        # Remove the brand name from the beginning
        if full_model.lower().startswith(brand.lower()):
            model_part = full_model[len(brand):].strip()
        else:
            # Fallback: split by space and take everything after first word
            parts = full_model.split(' ', 1)
            model_part = parts[1] if len(parts) > 1 else full_model
        
        return model_part
    
    def generate_variations(self, model: str) -> Set[str]:
        """Generate all possible variations of a model"""
        variations = {model}
        
        # Hyphen variations
        if '-' in model:
            variations.add(model.replace('-', ''))
            variations.add(model.replace('-', ' '))
        
        # Space variations  
        if ' ' in model:
            variations.add(model.replace(' ', ''))
            variations.add(model.replace(' ', '-'))
        
        # Add hyphens before numbers if not present
        no_hyphen_before_num = re.sub(r'([A-Za-z])(\d)', r'\1-\2', model)
        if no_hyphen_before_num != model:
            variations.add(no_hyphen_before_num)
            
        return variations
    
    def find_synonym_groups(self) -> List[Set[str]]:
        """Find groups of models that should be synonyms"""
        # Create a mapping from normalized model to all its variations
        normalized_to_models = defaultdict(set)
        
        for model in self.model_parts:
            # Create a normalized version (remove spaces, hyphens, make uppercase)
            normalized = re.sub(r'[-\s]', '', model.upper())
            normalized_to_models[normalized].add(model)
            
            # Also generate variations and map them
            variations = self.generate_variations(model)
            for variation in variations:
                var_normalized = re.sub(r'[-\s]', '', variation.upper())
                if var_normalized == normalized:
                    normalized_to_models[normalized].add(variation)
        
        # Find groups where multiple actual models map to the same normalized form
        synonym_groups = []
        for normalized, models in normalized_to_models.items():
            # Only include models that actually exist in our dataset
            existing_models = set()
            for model in models:
                if model in self.model_parts:
                    existing_models.add(model)
            
            if len(existing_models) > 1:
                synonym_groups.append(existing_models)
        
        return synonym_groups
    
    def find_pattern_based_groups(self) -> List[Set[str]]:
        """Find synonym groups based on common patterns"""
        pattern_groups = defaultdict(set)
        
        for model in self.model_parts:
            # Extract base pattern (letters + numbers, ignoring suffixes)
            base_match = re.match(r'^([A-Za-z]+[-\s]?\d+)', model)
            if base_match:
                base = base_match.group(1)
                # Normalize the base (remove spaces/hyphens)
                normalized_base = re.sub(r'[-\s]', '', base.upper())
                pattern_groups[normalized_base].add(model)
        
        # Convert to list of sets, only keeping groups with multiple models
        groups = []
        for base, models in pattern_groups.items():
            if len(models) > 1:
                groups.append(models)
        
        return groups
    
    def generate_output(self) -> str:
        """Generate the final synonym output"""
        self.parse_input_file()
        
        # Get synonym groups from both methods
        synonym_groups = self.find_synonym_groups()
        pattern_groups = self.find_pattern_based_groups()
        
        # Combine and deduplicate groups
        all_groups = synonym_groups + pattern_groups
        
        # Remove duplicate groups
        unique_groups = []
        seen_combinations = set()
        
        for group in all_groups:
            # Sort the group to create a consistent signature
            sorted_group = tuple(sorted(group))
            if sorted_group not in seen_combinations and len(sorted_group) > 1:
                seen_combinations.add(sorted_group)
                unique_groups.append(group)
        
        print(f"Found {len(unique_groups)} synonym groups")
        
        # Generate output
        output_lines = ["# Defined synonyms.", ""]
        
        for group in unique_groups:
            if len(group) > 1:
                # Generate all variations for each model in the group
                all_variations = set()
                for model in group:
                    variations = self.generate_variations(model)
                    # Only add variations that exist in our dataset or are simple transformations
                    for variation in variations:
                        all_variations.add(variation)
                
                # Sort for consistent output
                sorted_variations = sorted(list(all_variations))
                if len(sorted_variations) > 1:
                    synonym_line = ", ".join(sorted_variations)
                    output_lines.append(synonym_line)
        
        return "\n".join(output_lines)

def main():
    generator = MachineToolSynonymGeneratorV2('titles_sorted_by_brand_frequency2ORMORE.txt')
    synonyms = generator.generate_output()
    
    # Write to output file
    with open('machine_tool_synonyms_v2.txt', 'w', encoding='utf-8') as f:
        f.write(synonyms)
    
    print("Synonyms generated successfully!")
    print(f"Output written to: machine_tool_synonyms_v2.txt")

if __name__ == "__main__":
    main()

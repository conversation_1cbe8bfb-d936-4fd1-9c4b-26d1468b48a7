#!/usr/bin/env python3
"""
Clean Synonyms - Remove overly broad terms
Removes generic industry terminology that could cause false matches
"""

import re
from typing import Set, List

def load_comprehensive_synonyms(filename: str) -> List[Set[str]]:
    """Load the comprehensive synonym groups"""
    synonym_groups = []

    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and ',' in line:
                synonyms = [s.strip() for s in line.split(',')]
                synonym_groups.append(set(synonyms))

    return synonym_groups

def identify_broad_terms() -> Set[str]:
    """Identify terms that are too broad and should be removed"""
    broad_terms = {
        # Generic machine types
        'VMC', 'Vertical Machining Center', 'Machining Center',
        'CNC Mill', 'CNC Lathe', 'Turning Center', 'Lathe',
        'Waterjet', 'Cutting System', 'Abrasive Waterjet',
        'Quick Turn Nexus', 'Super Turn',

        # Generic capabilities
        '5 Axis', '5-Axis', '5Axis',
        'Multi-Axis', 'Multi Axis',

        # Generic descriptors
        'Heavy Duty', 'High Performance', 'Precision',
        'Computer Numerical Control',

        # Website/source names (shouldn't be synonyms)
        'haas.com', 'mazakusa.com', 'machinery.com', 'machinetools.com',
        'bidspotter.com', 'waterjets.org', 'flowwaterjet.com',

        # Very generic terms
        'Machine', 'Tool', 'Equipment', 'System'
    }

    # Add case variations
    expanded_terms = set()
    for term in broad_terms:
        expanded_terms.add(term)
        expanded_terms.add(term.upper())
        expanded_terms.add(term.lower())
        expanded_terms.add(term.title())

    return expanded_terms

def clean_synonym_group(synonym_group: Set[str], broad_terms: Set[str]) -> Set[str]:
    """Remove broad terms from a synonym group"""
    cleaned_group = set()

    for synonym in synonym_group:
        # Check if this synonym is a broad term
        if synonym in broad_terms:
            continue

        # Check for specific problematic patterns
        is_broad = False

        # Remove anything with "VMC" or "Vertical Machining Center"
        if re.search(r'\b(VMC|Vertical\s+Machining\s+Center)\b', synonym, re.IGNORECASE):
            is_broad = True

        # Remove anything with "Waterjet" or "Cutting System"
        if re.search(r'\b(Waterjet|Cutting\s+System|Abrasive\s+Waterjet)\b', synonym, re.IGNORECASE):
            is_broad = True

        # Remove anything with "5 Axis" or similar
        if re.search(r'\b(5\s*Axis|Multi[-\s]*Axis)\b', synonym, re.IGNORECASE):
            is_broad = True

        # Remove anything with "CNC" as standalone
        if re.search(r'\bCNC\s+(Mill|Lathe|Machine)\b', synonym, re.IGNORECASE):
            is_broad = True

        # Remove anything with "Turning Center" or "Machining Center" as standalone
        if re.search(r'\b(Turning\s+Center|Machining\s+Center)$', synonym, re.IGNORECASE):
            is_broad = True

        # Remove brand prefixes that are too generic (Flow, Haas, etc. when standalone)
        if re.match(r'^(Flow|Haas|Mazak|Okuma|Doosan)\s+[A-Z]', synonym) and not re.search(r'[A-Z]+[-\s]?\d+', synonym):
            is_broad = True

        # Check if this synonym contains broad terms as standalone words
        for broad_term in broad_terms:
            if re.search(r'\b' + re.escape(broad_term) + r'\b', synonym, re.IGNORECASE):
                is_broad = True
                break

        if not is_broad:
            cleaned_group.add(synonym)

    return cleaned_group

def clean_all_synonyms(input_file: str, output_file: str):
    """Clean all synonym groups and write to output file"""
    print("Loading comprehensive synonyms...")
    synonym_groups = load_comprehensive_synonyms(input_file)
    print(f"Loaded {len(synonym_groups)} synonym groups")

    print("Identifying broad terms to remove...")
    broad_terms = identify_broad_terms()
    print(f"Identified {len(broad_terms)} broad terms")

    print("Cleaning synonym groups...")
    cleaned_groups = []
    removed_count = 0

    for i, group in enumerate(synonym_groups):
        original_size = len(group)
        cleaned_group = clean_synonym_group(group, broad_terms)

        # Only keep groups with multiple synonyms
        if len(cleaned_group) > 1:
            cleaned_groups.append(cleaned_group)
            removed_this_group = original_size - len(cleaned_group)
            removed_count += removed_this_group

            if removed_this_group > 0:
                print(f"  Group {i+1}: Removed {removed_this_group} broad terms")
        else:
            print(f"  Group {i+1}: Removed entire group (too few remaining synonyms)")

    print(f"\nCleaning complete:")
    print(f"  Original groups: {len(synonym_groups)}")
    print(f"  Cleaned groups: {len(cleaned_groups)}")
    print(f"  Total terms removed: {removed_count}")

    # Write cleaned results
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# Cleaned machine tool synonyms - broad terms removed\n")
        f.write("# Focused on specific model variations only\n\n")

        for group in cleaned_groups:
            if len(group) > 1:
                sorted_group = sorted(list(group))
                f.write(", ".join(sorted_group) + "\n")

    print(f"\nCleaned synonyms written to: {output_file}")

    # Show examples of what was removed
    print(f"\nExamples of broad terms removed:")
    sample_broad = list(broad_terms)[:10]
    for term in sample_broad:
        print(f"  - {term}")

def main():
    try:
        clean_all_synonyms(
            'machine_tool_synonyms_comprehensive.txt',
            'machine_tool_synonyms_cleaned.txt'
        )
    except FileNotFoundError:
        print("Comprehensive synonyms file not found. Please ensure machine_tool_synonyms_comprehensive.txt exists.")

if __name__ == "__main__":
    main()

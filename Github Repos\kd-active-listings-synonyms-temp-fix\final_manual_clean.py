#!/usr/bin/env python3
"""
Final Manual Clean - Remove remaining problematic terms manually
"""

def clean_final_synonyms():
    """Manually clean the remaining problematic terms"""
    
    # Read the ultra clean file
    with open('machine_tool_synonyms_ultra_clean.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    cleaned_lines = []
    
    for line in lines:
        if line.startswith('#') or not line.strip():
            cleaned_lines.append(line)
            continue
        
        # Split into synonyms
        synonyms = [s.strip() for s in line.split(',')]
        
        # Remove problematic terms
        problematic_terms = {
            'Standard-Scrap-Reclaim-Line',
            'Standard-Single-Screw',
            'Standard-Single-Screw-Extruder',
            'StandardScrapReclaimLine',
            'StandardSingleScrew', 
            'StandardSingleScrewExtruder'
        }
        
        # Filter out problematic terms
        cleaned_synonyms = []
        for synonym in synonyms:
            if synonym not in problematic_terms:
                cleaned_synonyms.append(synonym)
        
        # Only keep groups with multiple synonyms
        if len(cleaned_synonyms) > 1:
            cleaned_line = ", ".join(cleaned_synonyms) + "\n"
            cleaned_lines.append(cleaned_line)
    
    # Write the final clean file
    with open('machine_tool_synonyms_production_ready.txt', 'w', encoding='utf-8') as f:
        f.write("# Production-ready machine tool synonyms\n")
        f.write("# All problematic terms removed - ready for Elasticsearch\n\n")
        
        for line in cleaned_lines[2:]:  # Skip the header lines
            if line.strip():
                f.write(line)
    
    print("Final production-ready synonyms created!")
    print("File: machine_tool_synonyms_production_ready.txt")

if __name__ == "__main__":
    clean_final_synonyms()

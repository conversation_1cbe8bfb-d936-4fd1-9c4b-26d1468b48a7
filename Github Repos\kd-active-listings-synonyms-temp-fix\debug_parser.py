#!/usr/bin/env python3
"""
Debug parser to understand the file format
"""

import re

def debug_parse():
    with open('titles_sorted_by_brand_frequency2ORMORE.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"Total lines in file: {len(lines)}")
    
    for i, line in enumerate(lines[:30]):  # First 30 lines
        line_stripped = line.strip()
        print(f"Line {i+1:2d}: '{line_stripped}'")
        
        # Test brand header regex
        brand_match = re.match(r'^([A-Z\-\s]+)\s+\((\d+)\s+machines\)$', line_stripped)
        if brand_match:
            print(f"    -> BRAND: {brand_match.group(1)}")
        
        # Test if it's a model line
        if line_stripped.startswith('  ') and not line_stripped.startswith('---'):
            print(f"    -> MODEL: {line_stripped}")

if __name__ == "__main__":
    debug_parse()

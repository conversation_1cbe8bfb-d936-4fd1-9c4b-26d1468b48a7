#!/usr/bin/env python3
"""
Check WordPress Synonym Coverage
Verify that all WordPress synonyms are included in the production-ready file
"""

def load_production_synonyms():
    """Load all synonyms from the production-ready file"""
    all_synonyms = set()
    
    with open('machine_tool_synonyms_production_ready.txt', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and ',' in line:
                synonyms = [s.strip() for s in line.split(',')]
                all_synonyms.update(synonyms)
    
    return all_synonyms

def get_wordpress_synonyms():
    """Get all synonyms from the WordPress list"""
    wordpress_groups = [
        ["VF-2", "VF2"],
        ["VF-3", "VF3", "VF-3YT", "VF3YT", "VF-3SS", "VF3SS", "VF-3SSYT", "VF3SSYT", "VF-3B", "VF3B", "VF-3SSAPC", "VF3SSAPC"],
        ["VF-4", "VF4"],
        ["VF-5", "VF5"],
        ["VF-6", "VF6"],
        ["VF-12", "VF12", "VF-12/40", "VF12/40", "VF-12/50", "VF12/50"],
        ["20P", "20 P"],
        ["Charmilles", "Agie", "AgieCharmilles"],
        ["Lynx 220", "Lynx 220LY", "Lynx 220LC", "Lynx 220LSY", "Lynx 220LSYA", "Lynx 220LSYC"],
        ["Amada FOL-3015AJ", "Amada FOL3015AJ", "Amada FO", "Amada FOL", "Amada FO 3015", "Amada FO3015", "Amada FO 3015NT", "Amada FO3015NT", "Amada FOM2 RI 3015 NT", "Amada FOM2RI3015NT", "Amada FOM2 3015 NT", "Amada FOM23015NT", "Amada FOMII 3015NT", "Amada FOMII3015NT", "Amada GEMINI FO 3015", "Amada GEMINI FO3015"],
        ["Amada ENSIS 3015 AJ", "Amada ENSIS 3015AJ", "Amada ENSIS3015AJ", "Amada ENSIS 3015", "Amada ENSIS3015", "Amada ENSIS 3015 RI", "Amada ENSIS3015RI", "Amada ENSIS 3015RI"],
        ["Amada EG 6013", "Amada EG6013"],
        ["Blanchard 3260", "Blanchard 32-60", "Blanchard 32HD-60"],
        ["Blanchard 18-36", "Blanchard 1836"],
        ["Makino U3", "Makino U3 HEAT", "Makino U3HEAT"],
        ["Nakamura AS-200", "Nakamura AS-200LMYS", "Nakamura AS200", "Nakamura AS200LMYS", "Nakamura-Tome AS-200", "Nakamura-Tome AS-200LMYS", "Nakamura-Tome AS200", "Nakamura-Tome AS200LMYS"],
        ["ST-10", "ST10", "ST-10T", "ST10T", "ST-10Y", "ST10Y"],
        ["Mach 3", "Mach3"],
        ["Mach 4", "Mach4"],
        ["Millac 853PF-5X", "Millac 853PF5X", "Millac 853"],
        ["Integrex i500V", "Integrex i-500V", "Integrex i500", "Integrex i-500", "Integrex 500", "Integrex 500V"]
    ]
    
    all_wordpress_synonyms = set()
    for group in wordpress_groups:
        all_wordpress_synonyms.update(group)
    
    return wordpress_groups, all_wordpress_synonyms

def main():
    print("Checking WordPress synonym coverage in production-ready file...")
    
    # Load data
    production_synonyms = load_production_synonyms()
    wordpress_groups, all_wordpress_synonyms = get_wordpress_synonyms()
    
    print(f"Production file contains: {len(production_synonyms)} total synonyms")
    print(f"WordPress list contains: {len(all_wordpress_synonyms)} total synonyms")
    
    # Check coverage
    missing_synonyms = []
    covered_groups = 0
    
    print(f"\nChecking coverage by group:")
    
    for i, group in enumerate(wordpress_groups, 1):
        group_missing = []
        for synonym in group:
            if synonym not in production_synonyms:
                group_missing.append(synonym)
        
        if group_missing:
            print(f"  Group {i:2d}: MISSING {len(group_missing)}/{len(group)} - {group_missing}")
            missing_synonyms.extend(group_missing)
        else:
            print(f"  Group {i:2d}: ✅ COMPLETE - {group[0]} group")
            covered_groups += 1
    
    print(f"\nSUMMARY:")
    print(f"  Groups fully covered: {covered_groups}/{len(wordpress_groups)}")
    print(f"  Total missing synonyms: {len(missing_synonyms)}")
    
    if missing_synonyms:
        print(f"\nMISSING SYNONYMS:")
        for synonym in sorted(missing_synonyms):
            print(f"  - {synonym}")
    else:
        print(f"\n✅ ALL WordPress synonyms are covered in the production file!")

if __name__ == "__main__":
    main()

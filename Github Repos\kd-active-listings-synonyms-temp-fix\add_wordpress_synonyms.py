#!/usr/bin/env python3
"""
Add Missing WordPress Synonyms
Add all missing WordPress synonyms to the production-ready file
"""

def load_production_synonyms():
    """Load existing production synonyms"""
    synonym_groups = []
    
    with open('machine_tool_synonyms_production_ready.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Keep header lines
    header_lines = []
    for line in lines:
        if line.startswith('#') or not line.strip():
            header_lines.append(line)
        elif ',' in line:
            synonyms = [s.strip() for s in line.split(',')]
            synonym_groups.append(set(synonyms))
    
    return header_lines, synonym_groups

def get_missing_wordpress_groups():
    """Get the WordPress synonym groups that are missing or incomplete"""
    missing_groups = [
        # VF-3 additions (missing VF-3B, VF3B, VF-3SSAPC, VF3SSAPC)
        {"VF-3B", "VF3B", "VF-3SSAPC", "VF3SSAPC"},
        
        # Completely missing groups
        {"VF-6", "VF6"},
        {"VF-12", "VF12", "VF-12/40", "VF12/40", "VF-12/50", "VF12/50"},
        {"20P", "20 P"},
        {"Charmilles", "Agie", "AgieCharmilles"},
        {"Lynx 220", "Lynx 220LY", "Lynx 220LC", "Lynx 220LSY", "Lynx 220LSYA", "Lynx 220LSYC"},
        {"Amada FOL-3015AJ", "Amada FOL3015AJ", "Amada FO", "Amada FOL", "Amada FO 3015", "Amada FO3015", "Amada FO 3015NT", "Amada FO3015NT", "Amada FOM2 RI 3015 NT", "Amada FOM2RI3015NT", "Amada FOM2 3015 NT", "Amada FOM23015NT", "Amada FOMII 3015NT", "Amada FOMII3015NT", "Amada GEMINI FO 3015", "Amada GEMINI FO3015"},
        {"Amada ENSIS 3015 AJ", "Amada ENSIS 3015AJ", "Amada ENSIS3015AJ", "Amada ENSIS 3015", "Amada ENSIS3015", "Amada ENSIS 3015 RI", "Amada ENSIS3015RI", "Amada ENSIS 3015RI"},
        {"Amada EG 6013", "Amada EG6013"},
        {"Blanchard 3260", "Blanchard 32-60", "Blanchard 32HD-60"},
        {"Blanchard 18-36", "Blanchard 1836"},
        {"Makino U3", "Makino U3 HEAT", "Makino U3HEAT"},
        {"Nakamura AS-200", "Nakamura AS-200LMYS", "Nakamura AS200", "Nakamura AS200LMYS", "Nakamura-Tome AS-200", "Nakamura-Tome AS-200LMYS", "Nakamura-Tome AS200", "Nakamura-Tome AS200LMYS"},
        {"ST-10T", "ST10T"},  # Add to existing ST-10 group
        {"Mach 3", "Mach3"},
        {"Mach 4", "Mach4"},
        {"Millac 853PF-5X", "Millac 853PF5X", "Millac 853"},
        {"Integrex i500V", "Integrex i-500V", "Integrex i500", "Integrex i-500", "Integrex 500", "Integrex 500V"}
    ]
    
    return missing_groups

def merge_with_existing_groups(existing_groups, new_groups):
    """Merge new groups with existing ones where there's overlap"""
    merged_groups = []
    used_new_groups = set()
    
    for existing_group in existing_groups:
        merged_group = set(existing_group)
        
        # Check if any new group should be merged with this existing group
        for i, new_group in enumerate(new_groups):
            if i in used_new_groups:
                continue
                
            # Check for overlap
            if existing_group.intersection(new_group):
                merged_group.update(new_group)
                used_new_groups.add(i)
        
        merged_groups.append(merged_group)
    
    # Add completely new groups that didn't merge
    for i, new_group in enumerate(new_groups):
        if i not in used_new_groups:
            merged_groups.append(new_group)
    
    return merged_groups

def main():
    print("Adding missing WordPress synonyms to production file...")
    
    # Load existing data
    header_lines, existing_groups = load_production_synonyms()
    print(f"Loaded {len(existing_groups)} existing synonym groups")
    
    # Get missing WordPress groups
    missing_groups = get_missing_wordpress_groups()
    print(f"Adding {len(missing_groups)} WordPress synonym groups")
    
    # Merge with existing groups
    merged_groups = merge_with_existing_groups(existing_groups, missing_groups)
    print(f"Result: {len(merged_groups)} total synonym groups")
    
    # Write the updated file
    with open('machine_tool_synonyms_production_ready.txt', 'w', encoding='utf-8') as f:
        # Write headers
        for header_line in header_lines:
            f.write(header_line)
        
        # Write all synonym groups
        for group in merged_groups:
            if len(group) > 1:
                sorted_group = sorted(list(group))
                f.write(", ".join(sorted_group) + "\n")
    
    print("✅ Successfully added all missing WordPress synonyms!")
    print("Updated file: machine_tool_synonyms_production_ready.txt")
    
    # Verify coverage
    print("\nVerifying WordPress coverage...")
    import subprocess
    subprocess.run(["python", "check_wordpress_coverage.py"])

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Machine Tool Synonym Generator - Final Version
Corrected parsing and comprehensive synonym generation
"""

import re
from collections import defaultdict
from typing import Dict, List, Set

def parse_input_file(filename: str) -> Dict[str, List[str]]:
    """Parse the input file and extract brand/model data"""
    brands_data = {}
    current_brand = None
    
    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            
            # Skip empty lines and headers
            if not line or line.startswith('Machine Titles') or line.startswith('Total') or line.startswith('='):
                continue
            
            # Check if this is a brand header
            brand_match = re.match(r'^([A-Z\-\s]+)\s+\((\d+)\s+machines\)$', line)
            if brand_match:
                current_brand = brand_match.group(1).strip()
                brands_data[current_brand] = []
                continue
            
            # Check if this is a separator line
            if line.startswith('---'):
                continue
            
            # This should be a machine model (no indentation check needed)
            if current_brand and not line.startswith('---') and line:
                brands_data[current_brand].append(line)
    
    return brands_data

def extract_model_part(full_model: str, brand: str) -> str:
    """Extract just the model part from 'Brand Model' format"""
    # Remove the brand name from the beginning (case insensitive)
    if full_model.lower().startswith(brand.lower()):
        model_part = full_model[len(brand):].strip()
        return model_part
    else:
        # Fallback: split by space and take everything after first word
        parts = full_model.split(' ', 1)
        return parts[1] if len(parts) > 1 else full_model

def generate_variations(model: str) -> Set[str]:
    """Generate all possible variations of a model"""
    variations = {model}
    
    # Hyphen variations
    if '-' in model:
        no_hyphen = model.replace('-', '')
        variations.add(no_hyphen)
        
        # Also try with spaces instead of hyphens
        with_spaces = model.replace('-', ' ')
        variations.add(with_spaces)
    
    # Space variations  
    if ' ' in model:
        no_space = model.replace(' ', '')
        variations.add(no_space)
        
        # Also try with hyphens instead of spaces
        with_hyphens = model.replace(' ', '-')
        variations.add(with_hyphens)
    
    return variations

def extract_base_pattern(model: str) -> str:
    """Extract the base pattern from a model name"""
    # Remove common suffixes and normalize
    model_upper = model.upper()
    
    # Look for letter-number patterns like VF-2, QTN-250, etc.
    match = re.match(r'^([A-Z]+[-\s]?\d+)', model_upper)
    if match:
        base = match.group(1)
        # Normalize by removing spaces and hyphens
        normalized = re.sub(r'[-\s]', '', base)
        return normalized
    
    # For models without clear patterns, use first part
    parts = re.split(r'[-\s]', model)
    if parts:
        return parts[0].upper()
    
    return model.upper()

def find_synonym_groups(all_model_parts: List[str], existing_models: Set[str]) -> List[Set[str]]:
    """Find groups of models that should be synonyms"""
    # Group models by their base pattern
    pattern_groups = defaultdict(set)
    
    for model in all_model_parts:
        base_pattern = extract_base_pattern(model)
        pattern_groups[base_pattern].add(model)
    
    # Generate synonym groups
    synonym_groups = []
    
    for pattern, model_set in pattern_groups.items():
        if len(model_set) > 1:
            # Generate all variations for models in this group
            all_variations = set()
            for model in model_set:
                variations = generate_variations(model)
                # Only include variations that make sense
                for variation in variations:
                    all_variations.add(variation)
            
            # Only keep groups with multiple distinct variations
            if len(all_variations) > 1:
                synonym_groups.append(all_variations)
    
    return synonym_groups

def main():
    # Parse the input file
    brands_data = parse_input_file('titles_sorted_by_brand_frequency2ORMORE.txt')
    total_models = sum(len(models) for models in brands_data.values())
    print(f"Parsed {total_models} total models from {len(brands_data)} brands")
    
    # Extract all model parts (without brand names)
    all_model_parts = []
    for brand, models in brands_data.items():
        for full_model in models:
            model_part = extract_model_part(full_model, brand)
            all_model_parts.append(model_part)
    
    print(f"Extracted {len(all_model_parts)} model parts")
    
    # Create a set of existing models for reference
    existing_models = set(all_model_parts)
    
    # Find synonym groups
    synonym_groups = find_synonym_groups(all_model_parts, existing_models)
    print(f"Found {len(synonym_groups)} synonym groups")
    
    # Remove duplicates
    unique_groups = []
    seen_combinations = set()
    
    for group in synonym_groups:
        # Sort the group to create a consistent signature
        sorted_group = tuple(sorted(group))
        if sorted_group not in seen_combinations and len(sorted_group) > 1:
            seen_combinations.add(sorted_group)
            unique_groups.append(group)
    
    print(f"Final unique groups: {len(unique_groups)}")
    
    # Generate output in the required format
    output_lines = ["# Defined synonyms.", ""]
    
    for group in unique_groups:
        if len(group) > 1:
            # Sort for consistent output
            sorted_variations = sorted(list(group))
            synonym_line = ", ".join(sorted_variations)
            output_lines.append(synonym_line)
    
    # Write to output file
    output_content = "\n".join(output_lines)
    with open('machine_tool_synonyms_final.txt', 'w', encoding='utf-8') as f:
        f.write(output_content)
    
    print("Synonyms generated successfully!")
    print(f"Output written to: machine_tool_synonyms_final.txt")
    
    # Show first few groups for verification
    if unique_groups:
        print("\nFirst few synonym groups:")
        for i, group in enumerate(unique_groups[:10]):
            sorted_group = sorted(list(group))
            print(f"  {i+1}: {', '.join(sorted_group)}")

if __name__ == "__main__":
    main()

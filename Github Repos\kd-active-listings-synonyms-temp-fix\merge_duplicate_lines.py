#!/usr/bin/env python3
"""
Merge Duplicate Lines
Properly merge the WordPress synonyms into existing lines instead of creating duplicates
"""

def merge_synonyms():
    """Merge duplicate synonym groups"""
    
    # Read the current file
    with open('machine_tool_synonyms_production_ready.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Separate headers and synonym lines
    header_lines = []
    synonym_lines = []
    
    for line in lines:
        if line.startswith('#') or not line.strip():
            header_lines.append(line)
        elif ',' in line:
            synonyms = [s.strip() for s in line.split(',')]
            synonym_lines.append(set(synonyms))
    
    print(f"Found {len(synonym_lines)} synonym groups")
    
    # Merge groups that have overlapping synonyms
    merged_groups = []
    used_indices = set()
    
    for i, group1 in enumerate(synonym_lines):
        if i in used_indices:
            continue
            
        merged_group = set(group1)
        used_indices.add(i)
        
        # Check for overlaps with remaining groups
        for j, group2 in enumerate(synonym_lines[i+1:], i+1):
            if j in used_indices:
                continue
                
            # If there's any overlap, merge the groups
            if group1.intersection(group2):
                merged_group.update(group2)
                used_indices.add(j)
                print(f"Merged group {i+1} with group {j+1}")
        
        if len(merged_group) > 1:
            merged_groups.append(merged_group)
    
    print(f"After merging: {len(merged_groups)} synonym groups")
    
    # Write the merged file
    with open('machine_tool_synonyms_production_ready.txt', 'w', encoding='utf-8') as f:
        # Write headers
        for header_line in header_lines:
            f.write(header_line)
        
        # Write merged synonym groups
        for group in merged_groups:
            if len(group) > 1:
                sorted_group = sorted(list(group))
                f.write(", ".join(sorted_group) + "\n")
    
    print("✅ Successfully merged duplicate synonym groups!")

if __name__ == "__main__":
    merge_synonyms()

#!/usr/bin/env python3
"""
Ultra Clean Synonyms - Remove only the truly generic problematic terms
"""

import re
from typing import Set, List

def load_synonyms(filename: str) -> List[Set[str]]:
    """Load synonym groups from file"""
    synonym_groups = []

    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and ',' in line:
                synonyms = [s.strip() for s in line.split(',')]
                synonym_groups.append(set(synonyms))

    return synonym_groups

def is_truly_problematic_term(term: str) -> bool:
    """Check if a term is truly generic and should be removed"""

    # Specific problematic terms to remove (including all variations)
    problematic_patterns = [
        'Standard Scrap Reclaim Line',
        'Standard-Scrap-Reclaim-Line',
        'StandardScrapReclaimLine',
        'Standard Single Screw',
        'Standard-Single-Screw',
        'StandardSingleScrew',
        'Standard Single Screw Extruder',
        'Standard-Single-Screw-Extruder',
        'StandardSingleScrewExtruder',
        '1000',
        '1000 Series',
        '1000-Series',
        '1000Series',
        '18'
    ]

    # Check exact matches (case insensitive)
    for pattern in problematic_patterns:
        if term.lower() == pattern.lower():
            return True

    return False

def clean_synonym_group(group: Set[str]) -> Set[str]:
    """Remove truly problematic terms from a synonym group"""
    cleaned = set()

    for term in group:
        if not is_truly_problematic_term(term):
            cleaned.add(term)

    return cleaned

def main():
    # Load the cleaned synonyms
    try:
        synonym_groups = load_synonyms('machine_tool_synonyms_final_clean.txt')
        print(f"Loaded {len(synonym_groups)} synonym groups")
    except FileNotFoundError:
        print("Final clean synonyms file not found.")
        return

    # Clean each group
    ultra_cleaned_groups = []
    total_removed = 0

    for i, group in enumerate(synonym_groups):
        original_size = len(group)
        cleaned_group = clean_synonym_group(group)

        # Only keep groups with multiple synonyms
        if len(cleaned_group) > 1:
            ultra_cleaned_groups.append(cleaned_group)
            removed_count = original_size - len(cleaned_group)
            total_removed += removed_count

            if removed_count > 0:
                print(f"Group {i+1}: Removed {removed_count} truly problematic terms")
        else:
            print(f"Group {i+1}: Removed entire group (insufficient remaining terms)")

    print(f"\nUltra cleaning summary:")
    print(f"  Original groups: {len(synonym_groups)}")
    print(f"  Ultra cleaned groups: {len(ultra_cleaned_groups)}")
    print(f"  Total terms removed: {total_removed}")

    # Write ultra cleaned results
    output_file = 'machine_tool_synonyms_ultra_clean.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# Ultra cleaned machine tool synonyms\n")
        f.write("# Only truly generic terms removed - ready for Elasticsearch\n\n")

        for group in ultra_cleaned_groups:
            if len(group) > 1:
                sorted_group = sorted(list(group))
                f.write(", ".join(sorted_group) + "\n")

    print(f"\nUltra cleaned synonyms written to: {output_file}")

    # Show what was removed
    removed_terms = [
        'Standard Scrap Reclaim Line',
        'Standard Single Screw',
        'Standard Single Screw Extruder',
        '1000',
        '1000 Series',
        '18'
    ]

    print(f"\nRemoved truly problematic terms:")
    for term in removed_terms:
        print(f"  - {term}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Final Clean Synonyms - Direct approach to remove problematic terms
"""

import re
from typing import Set, List

def load_synonyms(filename: str) -> List[Set[str]]:
    """Load synonym groups from file"""
    synonym_groups = []
    
    with open(filename, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and ',' in line:
                synonyms = [s.strip() for s in line.split(',')]
                synonym_groups.append(set(synonyms))
    
    return synonym_groups

def is_problematic_term(term: str) -> bool:
    """Check if a term is too broad and should be removed"""
    
    # Direct problematic terms
    problematic_exact = {
        'VMC VF-2', 'Vertical Machining Center VF-2',
        'VMC VF-4', 'Vertical Machining Center VF-4',
        'VMC VF-3', 'Vertical Machining Center VF-3',
        'VMC VF-5', 'Vertical Machining Center VF-5',
        'VMC VF-6', 'Vertical Machining Center VF-6',
        'Flow Mach 3 4020b',
        'Waterjet Mach 3 4020b',
        '5 Axis', '5-Axis', '5Axis',
        'Multi Axis', 'Multi-Axis', 'MultiAxis',
        'CNC Mill', 'CNC Lathe', 'CNC Machine',
        'Turning Center', 'Machining Center',
        'Waterjet', 'Cutting System', 'Abrasive Waterjet',
        'Electric Fume Extractor'
    }
    
    # Check exact matches (case insensitive)
    if term.lower() in {t.lower() for t in problematic_exact}:
        return True
    
    # Check patterns
    patterns = [
        r'^VMC\s+',  # Starts with "VMC "
        r'^Vertical\s+Machining\s+Center\s+',  # Starts with "Vertical Machining Center "
        r'^Waterjet\s+',  # Starts with "Waterjet "
        r'^CNC\s+(Mill|Lathe|Machine)$',  # CNC + generic type
        r'^(Turning|Machining)\s+Center$',  # Generic center types
        r'\b5\s*Axis\b',  # 5 Axis anywhere
        r'\bMulti[-\s]*Axis\b',  # Multi Axis anywhere
    ]
    
    for pattern in patterns:
        if re.search(pattern, term, re.IGNORECASE):
            return True
    
    return False

def clean_synonym_group(group: Set[str]) -> Set[str]:
    """Remove problematic terms from a synonym group"""
    cleaned = set()
    
    for term in group:
        if not is_problematic_term(term):
            cleaned.add(term)
    
    return cleaned

def main():
    # Load the comprehensive synonyms
    try:
        synonym_groups = load_synonyms('machine_tool_synonyms_comprehensive.txt')
        print(f"Loaded {len(synonym_groups)} synonym groups")
    except FileNotFoundError:
        print("Comprehensive synonyms file not found.")
        return
    
    # Clean each group
    cleaned_groups = []
    total_removed = 0
    
    for i, group in enumerate(synonym_groups):
        original_size = len(group)
        cleaned_group = clean_synonym_group(group)
        
        # Only keep groups with multiple synonyms
        if len(cleaned_group) > 1:
            cleaned_groups.append(cleaned_group)
            removed_count = original_size - len(cleaned_group)
            total_removed += removed_count
            
            if removed_count > 0:
                print(f"Group {i+1}: Removed {removed_count} problematic terms")
        else:
            print(f"Group {i+1}: Removed entire group (insufficient remaining terms)")
    
    print(f"\nCleaning summary:")
    print(f"  Original groups: {len(synonym_groups)}")
    print(f"  Cleaned groups: {len(cleaned_groups)}")
    print(f"  Total terms removed: {total_removed}")
    
    # Write cleaned results
    output_file = 'machine_tool_synonyms_final_clean.txt'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# Final cleaned machine tool synonyms\n")
        f.write("# All broad/generic terms removed - focused on specific model variations only\n\n")
        
        for group in cleaned_groups:
            if len(group) > 1:
                sorted_group = sorted(list(group))
                f.write(", ".join(sorted_group) + "\n")
    
    print(f"\nFinal cleaned synonyms written to: {output_file}")
    
    # Show examples of what was cleaned
    print(f"\nExample of cleaned VF-2 group:")
    for group in cleaned_groups[:5]:
        if any('VF-2' in term for term in group):
            sorted_group = sorted(list(group))
            print(f"  {', '.join(sorted_group[:10])}{'...' if len(sorted_group) > 10 else ''}")
            break

if __name__ == "__main__":
    main()
